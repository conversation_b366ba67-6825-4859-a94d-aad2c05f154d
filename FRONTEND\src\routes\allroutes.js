import { createRouter, createRoute } from "@tanstack/react-router";
import { rootRoute } from "./routes";
import Home from "../pages/Home";
import Notes from "../pages/Notes";
import Flashcards from "../pages/Flashcards";
import Upload from "../pages/Upload";

const homeRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: "/",
  component: Home, // ✅ FIXED
});

const notesRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: "/notes",
  component: Notes,
});

const flashcardsRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: "/flashcards",
  component: Flashcards,
});

const uploadRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: "/upload",
  component: Upload,
});
export const router = createRouter({
  routeTree: rootRoute.addChildren([
    homeRoute,
    notesRoute,
    flashcardsRoute,
    uploadRoute,
  ]),
});
