import { createRouter, createRoute } from "@tanstack/react-router";
import { rootRoute } from "./routes.js";
import Notes from "../pages/Notes.jsx";
import Flashcards from "../pages/Flashcards.jsx";
import Upload from "../pages/Upload.jsx";

const homeRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: "/",
  component: () => Home,
});

const notesRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: "/notes",
  component: () => Notes,
});

const flashcardsRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: "/flashcards",
  component: () => Flashcards,
});

const uploadRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: "/upload",
  component: () => Upload,
});

export const router = createRouter({
  routeTree: rootRoute.addChildren([
    homeRoute,
    notesRoute,
    flashcardsRoute,
    uploadRoute,
  ]),
});
